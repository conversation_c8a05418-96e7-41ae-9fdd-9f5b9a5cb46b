{"name": "scb-batchjob-dlp-consumption", "version": "1.0.0", "description": "This repo contains all general consent logic", "main": "index.js", "scripts": {"start": "NODE_ENV=dev nodemon index.js", "local": "NODE_ENV=local nodemon index.js", "dev": "NODE_ENV=dev pm2 start index.js --name dev", "debug": "cross-env ./node_modules/ndb/ndb.js nodemon --require dotenv/config index.js ", "test": "jest --coverage", "test:watch": "jest --watchAll", "lint:fix": "./node_modules/eslint/bin/eslint.js --fix "}, "author": "Naiprakarn Nakornkhet", "license": "ISC", "devDependencies": {"@types/jest": "^25.2.3", "babel-eslint": "^10.1.0", "cross-env": "^7.0.2", "dotenv": "^8.2.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.24.0", "ndb": "^1.1.5", "nodemon": "^2.0.6", "prettier": "^2.2.1", "randexp": "^0.5.3", "supertest": "^6.1.3"}, "dependencies": {"aws-sdk": "^2.793.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "deepmerge": "^4.2.2", "fast-csv": "^5.0.2", "fastest-validator": "^1.9.0", "fs": "^0.0.1-security", "glob": "^7.1.6", "jest": "^29.7.0", "lodash": "^4.17.20", "microservice-config": "**************:scb-tx1970-partnereco/microservice-config-eks.git", "moment": "^2.30.1", "mysql2": "^3.14.1", "scb-partner-commons": "/Users/<USER>/naiprakarn/scb-partner-v2-commons-eks", "uuid": "^3.4.0", "vault-helper": "**************:scb-tx1970-partnereco/vault-helper.git"}}