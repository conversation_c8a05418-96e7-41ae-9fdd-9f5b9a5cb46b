const AWS = require('aws-sdk');
const { getAppFileContent } = require('../src/model/s3');

// Mock AWS SDK
jest.mock('aws-sdk');

// Mock requireSrc
global.requireSrc = function (name) {
  if (name === 'config') {
    return {
      aws: {
        region: 'ap-southeast-1',
        apiVersion: '2006-03-01',
        signatureVersion: 'v4',
        folder: 'fee_engine',
        fileName: 'fee_engine_master.json'
      },
      s3: {
        bucket_name: 'test-bucket',
        region: 'ap-southeast-1'
      }
    };
  }
  if (name === 'commons') {
    return {
      Log: {
        info: jest.fn(),
        debug: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }
    };
  }
};

describe('S3 getAppFileContent', () => {
  let mockS3Instance;
  let mockGetObject;

  beforeEach(() => {
    mockGetObject = jest.fn();
    mockS3Instance = {
      getObject: mockGetObject
    };
    AWS.S3.mockImplementation(() => mockS3Instance);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should successfully retrieve and parse JSON file from S3', async () => {
    const mockJsonData = { test: 'data', value: 123 };
    const mockResponse = {
      Body: Buffer.from(JSON.stringify(mockJsonData))
    };

    mockGetObject.mockImplementation((params, callback) => {
      callback(null, mockResponse);
    });

    const result = await getAppFileContent();
    
    expect(result).toEqual(mockJsonData);
    expect(mockGetObject).toHaveBeenCalledWith({
      Bucket: 'test-bucket',
      Key: 'fee_engine/fee_engine_master.json'
    }, expect.any(Function));
  });

  test('should return null when file does not exist (NoSuchKey)', async () => {
    const mockError = new Error('The specified key does not exist.');
    mockError.code = 'NoSuchKey';

    mockGetObject.mockImplementation((params, callback) => {
      callback(mockError, null);
    });

    const result = await getAppFileContent();
    
    expect(result).toBeNull();
  });

  test('should reject when bucket does not exist (NoSuchBucket)', async () => {
    const mockError = new Error('The specified bucket does not exist.');
    mockError.code = 'NoSuchBucket';

    mockGetObject.mockImplementation((params, callback) => {
      callback(mockError, null);
    });

    await expect(getAppFileContent()).rejects.toThrow('Bucket does not exist: test-bucket');
  });

  test('should reject when access is denied', async () => {
    const mockError = new Error('Access Denied');
    mockError.code = 'AccessDenied';

    mockGetObject.mockImplementation((params, callback) => {
      callback(mockError, null);
    });

    await expect(getAppFileContent()).rejects.toThrow('Access denied to S3 resource');
  });

  test('should reject when JSON parsing fails', async () => {
    const mockResponse = {
      Body: Buffer.from('invalid json content')
    };

    mockGetObject.mockImplementation((params, callback) => {
      callback(null, mockResponse);
    });

    await expect(getAppFileContent()).rejects.toThrow('Invalid JSON format in S3 file');
  });

  test('should reject when bucket_name is not configured', async () => {
    // Override the mock to return config without bucket_name
    global.requireSrc = function (name) {
      if (name === 'config') {
        return {
          aws: {
            region: 'ap-southeast-1',
            apiVersion: '2006-03-01',
            signatureVersion: 'v4',
            folder: 'fee_engine',
            fileName: 'fee_engine_master.json'
          },
          s3: {} // No bucket_name
        };
      }
      if (name === 'commons') {
        return {
          Log: {
            info: jest.fn(),
            debug: jest.fn(),
            warn: jest.fn(),
            error: jest.fn()
          }
        };
      }
    };

    // Re-require the module to pick up the new config
    jest.resetModules();
    const { getAppFileContent: getAppFileContentNew } = require('../src/model/s3');

    await expect(getAppFileContentNew()).rejects.toThrow('S3 bucket_name is not configured');
  });
});
