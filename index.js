const { enableVaultConfig } = require('vault-helper');
const dayjs = require('dayjs');
const customParseFormat = require('dayjs/plugin/customParseFormat');
dayjs.extend(customParseFormat);
const runBackdate = process.env.RUNBACKDATE;
if (runBackdate) {
  const isDateValid = dayjs(runBackdate, 'YYYYMMDD', true).isValid();
  if (!isDateValid) {
    process.exit(1);
  }
}

const startServer = (args) => {
  if (!global.requireSrc) {
    global.requireSrc = function (name) {
      return require(`${__dirname}/src/${name}`);
    };
  }
  const { Log: logger } = requireSrc('commons');
  const { serverIndex } = require('./src/server')
  logger.info('Starting server with args:', args);
  serverIndex (args)
}

const main = async (args) => {
  try {
    const listENV = ['dev', 'sit', 'uat', 'pt', 'prod'];
    if (listENV.includes(process.env.NODE_ENV)) {
      await enableVaultConfig('AP1970-PartnerEcosystem', ['DB_USER_PORTAL', 'DB_PASSWORD_PORTAL'], true, false);
    }
    startServer(args);
  } catch (error) {
    console.log('ERROR WHEN TRY TO START SERVER OR ENABLE VAULT CONFIG: ', error.response?.data || error.response || error);
  }
};

main(runBackdate);
