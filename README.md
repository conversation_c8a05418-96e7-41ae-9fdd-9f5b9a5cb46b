# scb-csent-v2-csent

This repo contains all general consent logic

## Installation

Assume your machine

```bash
git clone https://gitlab.com/scbtechx/scb-tx1970-partnereco/scb-batchjob-dlp-consumption
cd scb-batchjob-dlp-consumption
npm install
```

## How to setup and run the service in local machine

In order to get the service up and running, you need the [scb-batchjob-dlp-consumption](https://gitlab.com/scbtechx/scb-tx1970-partnereco/scb-batchjob-dlp-consumption) service up and running in port `5000` in the local development.


1. Create `config.local file and paste the content below

```bash
NODE_ENV=local
DB_USER=root
DB_PASSWORD=
DB_NAME=consent
DB_PORT=3306
KEY=<key>
SECRET=<secret>
```

2. Get the service up and running

```bash
yarn run start
# or
npm run start
```

3. Call the api route that defined in [`src/routes`](./src/routes)

## Testing

### Get test coverage and report

Just run the `npm run test` for get a coverage and test report.

### Development

For development just run `npm run test:watch`.

## Debugging

Sometimes you might want to deubgging the api.

Currently I'm using the Visual Studio Code for developing.

In this section, We'll provided you a configuration file for debug the api and debugging in your local machine.

Create a config file `.vscode/settings.json` and paste the line below

```json
{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/index.js",
      "env": {
        "NODE_ENV": "<NODE_ENV>",
        "DB_USER": "<DB_USER>",
        "DB_PASSWORD": "<DB_PASSWORD>",
        "DB_NAME": "<DB_NAME>",
        "DB_PORT": "<DB_PORT>",
        "KEY": "<KEY>",
        "SECRET": "<SECRET>"
      },
      "outputCapture": "std"
    },
    {
      "name": "Debug Jest Tests",
      "type": "node",
      "request": "launch",
      "runtimeArgs": [
        "--inspect-brk",
        "${workspaceRoot}/node_modules/.bin/jest",
        "--runInBand"
      ],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "port": 9229,
      "outputCapture": "std"
    }
  ]
}
```

then go to Run. You'll see the `Launch Program` and `Debug Jest Tests`.

Just select the `Launch Program` for debugging our api, set some breakpoint that you want to test, and some magic (like post, put, etc.) and the your IDE will be stopped at that breakpoint.

Or if you want to debug a jest test case just select the `Debug Jest Tests`.


## Fix eslint command

```bash
npm run lint:fix -- ./path/to/**/*.js
```
