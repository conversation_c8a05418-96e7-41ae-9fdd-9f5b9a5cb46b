/* eslint-disable camelcase */
const { promisify } = require('util')
const mysql = require('mysql2');

const config = requireSrc('config');

const rds_conf = config.rds_portal || {}
rds_conf.host = process.env.DB_HOST || rds_conf.host
rds_conf.user = process.env.DB_USER_PORTAL || rds_conf.user
rds_conf.password = process.env.DB_PASSWORD_PORTAL || rds_conf.password
rds_conf.port = process.env.DB_PORT || rds_conf.port || 3306

const pool = mysql.createPool(rds_conf);

pool.query = promisify(pool.query);

module.exports = { pool };
