const moment = require('moment');
const config = requireSrc('config');
const { Log: logger } = requireSrc('commons');
const AWS = require('aws-sdk');
const { region, apiVersion, signatureVersion, bucket_name, folder, fileName } = config.aws;
const s3 = new AWS.S3({ apiVersion, signatureVersion, region });


// get log file content from S3
const getAppFileContent = () => {
  console.log(`getAppFileContent()`); 
  return new Promise(async (resolve, reject) => {
    try {
      const params = { Bucket: bucket_name, Key: `${folder}/${fileName}` };
      console.log(`Searching file: ${params.Bucket}/${params.Key}`);

      s3.getObject(params, (error, data) => {
        if (error) {
          const errorType = (typeof error) === 'string' ? error.split(':')[0] : '';
          if(errorType === 'NoSuchKey') {
            console.log(`File does not exist`);
          } else {
            console.error(`s3.getObject() - error: ${error}`);
          }
          return resolve(null);
        }
        const response = data.Body.toString('utf-8');
        console.log(`File Content: ${response}`); 
        return resolve(JSON.parse(response));
      });
    } catch (error) {
      console.error(`getAppFileContent - error: ${error}`);
      return reject(null);
    }
  });
};


module.exports = {
  getAppFileContent
};