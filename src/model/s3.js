const config = requireSrc('config');
const { Log: logger } = requireSrc('commons');
const AWS = require('aws-sdk');

// Get configuration from both aws and s3 sections
const { region, apiVersion, signatureVersion, folder, fileName } = config.aws;
const { bucket_name } = config.s3 || config.aws;

// Configure S3 client with proper settings
const s3Config = {
  apiVersion: apiVersion || '2006-03-01',
  signatureVersion: signatureVersion || 'v4',
  region: region || config.s3?.region || 'ap-southeast-1'
};

const s3 = new AWS.S3(s3Config);

// get log file content from S3
const getAppFileContent = () => {
  logger.info(`getAppFileContent() - Starting S3 file retrieval`);

  return new Promise((resolve, reject) => {
    try {
      // Validate required parameters
      if (!bucket_name) {
        const error = new Error('S3 bucket_name is not configured');
        logger.error(`Configuration error: ${error.message}`);
        return reject(error);
      }

      if (!folder || !fileName) {
        const error = new Error('S3 folder or fileName is not configured');
        logger.error(`Configuration error: ${error.message}`);
        return reject(error);
      }

      const params = {
        Bucket: bucket_name,
        Key: `${folder}/${fileName}`
      };

      logger.info(`Searching file: ${params.Bucket}/${params.Key}`);

      s3.getObject(params, (error, data) => {
        if (error) {
          // Proper AWS SDK error handling
          if (error.code === 'NoSuchKey') {
            logger.warn(`File does not exist: ${params.Key}`);
            return resolve(null);
          } else if (error.code === 'NoSuchBucket') {
            logger.error(`Bucket does not exist: ${params.Bucket}`);
            return reject(new Error(`Bucket does not exist: ${params.Bucket}`));
          } else if (error.code === 'AccessDenied') {
            logger.error(`Access denied to S3 resource: ${params.Bucket}/${params.Key}`);
            return reject(new Error(`Access denied to S3 resource`));
          } else {
            logger.error(`S3 getObject error: ${error.code} - ${error.message}`);
            return reject(error);
          }
        }

        try {
          const response = data.Body.toString('utf-8');
          logger.debug(`File Content: ${response}`);

          // Parse JSON and handle parsing errors
          const jsonData = JSON.parse(response);
          return resolve(jsonData);
        } catch (parseError) {
          logger.error(`JSON parsing error: ${parseError.message}`);
          logger.debug(`Raw content that failed to parse: ${response}`);
          return reject(new Error(`Invalid JSON format in S3 file: ${parseError.message}`));
        }
      });
    } catch (error) {
      logger.error(`getAppFileContent - unexpected error: ${error.message}`);
      return reject(error);
    }
  });
};

module.exports = {
  getAppFileContent
};