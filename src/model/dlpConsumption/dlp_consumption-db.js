const dayjs = require('dayjs');
const { Log: logger } = Commons;
const { pool } = require('../mysql-pool')
const { timestamp } = requireSrc('config')

const tblName = 'dlp_consumption';
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)

module.exports = {
  getSuccessDlpConsumptionsByAppName: (runBackDate, appNameLists) => {
    logger.debug(`dlp_consumption-db getDlpConsumptionsByAppName()`);
    return new Promise(async (resolve, reject) => {
      try {
        const startDate = dayjs(runBackDate).tz(timestamp.timezone_local).startOf('day');
        const endDate = startDate.add(1, 'day');
        const startUnix = startDate.unix();
        const endUnix = endDate.unix();
        const tupleAppString = `(${appNameLists.map(item => `'${item}'`).join(',')})`;
        const query = `
          SELECT organizationname, applicationname, count(httpcode) as transactionVolume
          FROM ${tblName}
          WHERE requesttime >= ? AND requesttime < ? AND httpcode < 400
          AND applicationname in ${tupleAppString}
          AND path in ('/partners/v2/customers/profile', '/partners/v1/util/digital_id/profile')
          GROUP BY organizationname, applicationname
        `;
        logger.debug(`Query: ${query} [${startUnix}, ${endUnix}]`);
        const rows = await pool.query(query, [startUnix, endUnix]);

        if (rows) {
          resolve(rows);
        } else {
          logger.debug('NO DATA FOUND');
          resolve();
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`);
        const failureObject = {
          type: 'database_error',
          source: 'getDlpConsumptionsByAppName',
        };
        reject(failureObject);
      }
    });
  }
}