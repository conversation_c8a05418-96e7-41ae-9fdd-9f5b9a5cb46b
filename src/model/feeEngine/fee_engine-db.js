const dayjs = require('dayjs');
const { Log: logger } = Commons;
const { pool } = require('../mysql-pool')
const { timestamp } = requireSrc('config')

const tblName = 'fee_engine';
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)

module.exports = {
  getLatestFeeEngineId: () => {
    logger.debug(`feeEngine_consumption-db getLatestFeeEngineId()`);
    return new Promise(async (resolve, reject) => {
      try {
        const query = `
          SELECT transactionId
          FROM ${tblName}
          ORDER BY transactionId DESC
          LIMIT 1
        `;
        logger.debug(`Query: ${query} `);
        const rows = await pool.query(query);

        if (rows) {
          resolve(rows[0]);
        } else {
          logger.debug('NO DATA FOUND');
          resolve();
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`);
        const failureObject = {
          type: 'database_error',
          source: 'getLatestFeeEngineId',
        };
        reject(failureObject);
      }
    });
  },
  insertFeeEngine: (payload) => {
    logger.debug(`feeEngine_consumption-db insertFeeEngine()`)

    return new Promise(async (resolve, reject) => {
      try {
        const query = `INSERT IGNORE INTO ${tblName} VALUES ?`
        logger.debug('QUERY:', query)
        const result = await pool.query(query, [payload])
        logger.debug('RESULT:', result)

        resolve(result)
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'feeEngine_consumption-db',
        }
        reject(failureObject)
      }
    }) // End Promise()
  },
}