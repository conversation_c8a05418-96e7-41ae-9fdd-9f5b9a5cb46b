const { Log: logger } = requireSrc('commons');
const { DlpConsumptionDB } = requireSrc('model').DlpConsumption;
const { FeeEngineDB } = requireSrc('model').FeeEngine;
const { getAppFileContent } = requireSrc('model').S3;
const { timestamp } = requireSrc('config')
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)

const getFormattedDate = (args = null) => {
    if (args) {
        return args;
    }
    return (dayjs().tz(timestamp.timezone_local).subtract(1, 'day')).format('YYYYMMDD');
};

const convertToFeeIdStr = (num) => {
    if(num < 10) {
        return `0000${num}`
    } else if (num < 100) {
        return `000${num}`
    } else if (num < 1000) {
        return `00${num}`
    } else if (num < 10000) {
        return `0${num}`
    } else {
        return `${num}`
    }
}


const serverIndex = async (args) => {
    logger.info(`Fee Engine Consumption Start`);
    const startTime = Date.now();
    const runDate = getFormattedDate(args);
    logger.info(`Processing data for date: ${runDate}`);
    try {
        const feeEngineOrgApp = await getAppFileContent()
        const appNameLists = feeEngineOrgApp.map(feeItem => { return feeItem.applicationName })
        const latestFeeId = await FeeEngineDB.getLatestFeeEngineId()
        let numFeeId = 0
        if(latestFeeId)
        {
            numFeeId = +latestFeeId.transactionId
        }
        const data = await DlpConsumptionDB.getSuccessDlpConsumptionsByAppName(runDate, appNameLists);
        logger.info(`Retrieved ${data.length} records for processing.`);
        const transformFeeEngines = [];
        for (const [index,log] of data.entries()) {

            const feeTransactionId = convertToFeeIdStr(numFeeId + index + 1)
            const appDetail = feeEngineOrgApp.filter(app => app.applicationName == log.applicationname && app.organizationName == log.organizationname)[0]
            if(appDetail)
            {
                const logItemArray = []
                logItemArray.push(feeTransactionId),
                logItemArray.push(dayjs(runDate).tz(timestamp.timezone_local).format("YYYYMMDD")),
                logItemArray.push(appDetail.customerId),
                logItemArray.push(appDetail.accountId),
                logItemArray.push(appDetail.chargeCode),
                logItemArray.push(log.transactionVolume)
                
                transformFeeEngines.push(logItemArray)
            }

            
        }
        if(transformFeeEngines.length > 0)
        {
            await FeeEngineDB.insertFeeEngine(transformFeeEngines)
        }
        const elapsed = Date.now() - startTime;
        logger.info(`Fee Engine Consumption Finished. Time taken: ${elapsed} ms`);
        logger.info('Process completed successfully, exiting...');
        process.exit(0);
    } catch (error) {
        logger.error(`Fee Engine Consumption Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

module.exports = { serverIndex };