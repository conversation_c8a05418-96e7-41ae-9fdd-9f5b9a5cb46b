const { Log: logger } = requireSrc('commons');
const { DlpConsumptionDB } = requireSrc('model').DlpConsumption;
const { FeeEngineDB } = requireSrc('model').FeeEngine;
const { getAppFileContent } = requireSrc('model').S3;
const { timestamp } = requireSrc('config')
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)

const getFormattedDate = (args = null) => {
    if (args) {
        return args;
    }
    return (dayjs().tz(timestamp.timezone_local).subtract(1, 'day')).format('YYYYMMDD');
};

const convertToFeeIdStr = (num) => {
    if(num < 10) {
        return `0000${num}`
    } else if (num < 100) {
        return `000${num}`
    } else if (num < 1000) {
        return `00${num}`
    } else if (num < 10000) {
        return `0${num}`
    } else {
        return `${num}`
    }
}


const serverIndex = async (args) => {
    logger.info(`Fee Engine Consumption Start`);
    const startTime = Date.now();
    const runDate = getFormattedDate(args);
    logger.info(`Processing data for date: ${runDate}`);
    try {
        // const feeEngineOrgApp = getAppFileContent()
        const feeEngineOrgApp = [
            {
                "organizationName": "Stark Industries",
                "applicationName": "Stark Industries Portal",
                "customerId": "88103529017364998273640192837465012",
                "accountId": "**********",
                "chargeCode": "XF83746102"
            },
            {
                "organizationName": "Wayne Enterprises",
                "applicationName": "Wayne Enterprises CRM",
                "customerId": "91028374650172839401827364091827364",
                "accountId": "**********",
                "chargeCode": "WK29384756"
            },
            {
                "organizationName": "Cyberdyne Systems",
                "applicationName": "Cyberdyne Systems Skynet",
                "customerId": "11235813213455891442333776109871597",
                "accountId": "**********",
                "chargeCode": "T8A9876543"
            },
            {
                "organizationName": "Oscorp",
                "applicationName": "Oscorp Research",
                "customerId": "50019283746501827394857261524398701",
                "accountId": "**********",
                "chargeCode": "OS44556677"
            },
            {
                "organizationName": "Tyrell Corporation",
                "applicationName": "Tyrell Corporation Nexus",
                "customerId": "20191021204908151982061119821123198",
                "accountId": "**********",
                "chargeCode": "NX67890123"
            },
            {
                "organizationName": "Umbrella Corporation",
                "applicationName": "Umbrella Corp Hive",
                "customerId": "61728394058172635409182736409182734",
                "accountId": "**********",
                "chargeCode": "UM10293847"
            },
            {
                "organizationName": "ACME Corporation",
                "applicationName": "ACME Product Catalog",
                "customerId": "10029384756102938475610293847561029",
                "accountId": "**********",
                "chargeCode": "AC75647382"
            },
            {
                "organizationName": "Soylent Corporation",
                "applicationName": "Soylent Corp Distribution",
                "customerId": "22819374019283746501827394857123456",
                "accountId": "**********",
                "chargeCode": "SC20221122"
            },
            {
                "organizationName": "Buy n Large",
                "applicationName": "Buy n Large Retail",
                "customerId": "71625340918273640918273640918273645",
                "accountId": "**********",
                "chargeCode": "BNL54637281"
            },
            {
                "organizationName": "OCP",
                "applicationName": "Omni Consumer Products Security",
                "customerId": "84756372819283746501827394857293847",
                "accountId": "**********",
                "chargeCode": "OCP65473829"
            }
        ]
        const appNameLists = feeEngineOrgApp.map(feeItem => { return feeItem.applicationName })
        const latestFeeId = await FeeEngineDB.getLatestFeeEngineId()
        let numFeeId = 0
        if(latestFeeId)
        {
            numFeeId = +latestFeeId.transactionId
        }
        const data = await DlpConsumptionDB.getSuccessDlpConsumptionsByAppName(runDate, appNameLists);
        logger.info(`Retrieved ${data.length} records for processing.`);
        const transformFeeEngines = [];
        for (const [index,log] of data.entries()) {

            const feeTransactionId = convertToFeeIdStr(numFeeId + index + 1)
            const appDetail = feeEngineOrgApp.filter(app => app.applicationName == log.applicationname && app.organizationName == log.organizationname)[0]
            if(appDetail)
            {
                const logItemArray = []
                logItemArray.push(feeTransactionId),
                logItemArray.push(dayjs(runDate).tz(timestamp.timezone_local).format("YYYYMMDD")),
                logItemArray.push(appDetail.customerId),
                logItemArray.push(appDetail.accountId),
                logItemArray.push(appDetail.chargeCode),
                logItemArray.push(log.transactionVolume)
                
                transformFeeEngines.push(logItemArray)
            }

            
        }
        if(transformFeeEngines.length > 0)
        {
            await FeeEngineDB.insertFeeEngine(transformFeeEngines)
        }
        const elapsed = Date.now() - startTime;
        logger.info(`Fee Engine Consumption Finished. Time taken: ${elapsed} ms`);
        logger.info('Process completed successfully, exiting...');
        process.exit(0);
    } catch (error) {
        logger.error(`Fee Engine Consumption Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

module.exports = { serverIndex };