const dayjs = require('dayjs')
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
const deepmerge = require('deepmerge');
const microserviceConfig = require('microservice-config');
const { getConfigObject } = require('microservice-config/envparser');

// Initialize Day.js plugins
dayjs.extend(utc)
dayjs.extend(timezone)

const globals = {
  apiServiceName: 'batchjob-dlp-consumption',
  apiBasePath: 'batchjob',
  apiVersion: 'v1',
  msVersion: requireSrc('../package.json').version,
  timestamp: {
    formatString: process.env.DATE_FORMAT || 'YYYY-MM-DDTHH:mm:ssZ',
    gmt_local: process.env.DATE_GMT_LOCAL || 'TRUE',
    timezone_local: process.env.TIMEZONE_LOCAL || 'Asia/Bangkok'
  },
};
globals.timestamp.format = () => {
  return (globals.timestamp.gmt_local === 'TRUE')
    ? dayjs().tz(globals.timestamp.timezone_local).format(globals.timestamp.formatString)
    : dayjs().utc().format(globals.timestamp.formatString)
}

const configFromFile = require(`./config.${process.env.NODE_ENV || 'local'}.json`);
const staticConfig = deepmerge(microserviceConfig, deepmerge(globals, configFromFile));
const deploymentConfig = getConfigObject();
const config = deepmerge(staticConfig, deploymentConfig);

module.exports = config;